import 'dart:developer';
import 'dart:io';
import 'dart:convert';
import 'package:dankware/constants/strings.dart';
import 'package:dankware/controllers/color_scheme_controller.dart';
import 'package:dankware/functions/common.dart';
import 'package:dankware/functions/copy_to_clipboard.dart';
import 'package:dankware/functions/open_url.dart';
import 'package:dankware/widgets/custom_alert.dart';
import 'package:dankware/widgets/transparent_fab.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:dankware/widgets/transparent_appbar.dart';
import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/colors.dart';
import 'package:dankware/constants/variables.dart';
import 'package:dankware/widgets/toast.dart';
import 'package:get/get.dart';

class EmailLookupController extends GetxController {
  var counter = 0.0.obs;
  var dbPaths = <String>[].obs;
  var dbResults = <Map<String, dynamic>>[].obs;
  var emailRepResults = <String>[].obs;
  var maxCounter = 5.0.obs;
  var proxyNovaResults = <dynamic>[].obs;
  var psbdmpResults = <dynamic>[].obs;
  var qualityScoreResults = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    if (isDesktop) {
      sqfliteFfiInit();
    }
    _loadDBs();
  }

  void _loadDBs() async {
    final savedPaths = storage.read<List<dynamic>>('savedPaths') ?? [];
    for (String dbPath in savedPaths) {
      if (await File(dbPath).exists()) {
        dbPaths.add(dbPath);
        log('[loadDBs] dbPath: $dbPath');
      }
    }
    storage.write('savedPaths', dbPaths);
  }

  void _getQuery(String dbPath, bool atRequired) {
    Get.dialog(
      AlertDialog(
        content: TextField(
          autofocus: trueValue,
          decoration: InputDecoration(labelText: 'Query'),
          onSubmitted: (String value) {
            Get.back();
            if (value.isEmpty) {
              showSnackbar('Query was empty!');
            } else if (atRequired && !value.contains('@')) {
              showSnackbar('Query missing @');
            } else if (!atRequired && value.contains('@')) {
              showSnackbar('Query contains @');
            } else {
              log('[getQuery] Query: $value');
              log('[getQuery] dbPath: $dbPath');
              Get.toNamed(ROUTES.emailLookupResults, arguments: {'dbPath': dbPath, 'query': value});
            }
          },
        ),
      ),
    );
  }

  void _deleteFile(File file) {
    if (!isDesktop) {
      file.delete();
      log('[getDBs] deleted: ${getFileName(file.path)}');
    }
  }

  void _saveDBs() {
    for (String dbPath in dbPaths) {
      log('[saveDBs] dbPath: $dbPath');
    }
    storage.write('savedPaths', dbPaths);
  }

  void _getDBs() async {
    final selected = await FilePicker.platform.pickFiles(allowMultiple: trueValue);
    if (selected is! FilePickerResult) {
      showSnackbar('No files selected');
      return;
    }
    final selectedFiles = selected.paths.map((path) => File(path!)).toList();
    List<File> validFiles = [];
    bool invalidFileType = falseValue;

    for (File file in selectedFiles) {
      if (!file.path.endsWith('.db')) {
        _deleteFile(file);
        invalidFileType = trueValue;
      }
    }

    for (File file in selectedFiles) {
      if (file.path.endsWith('.db')) {
        validFiles.add(file);
        invalidFileType = falseValue;
      }
    }

    if (invalidFileType) {
      if (Get.isDialogOpen!) {
        Get.dialog(
          customAlert(Get.context!, 'Invalid file extension!', 'Please select files with .db extensions'),
        );
      }
    } else {
      dbPaths.addAll(validFiles.map((file) => file.path));
      _saveDBs();
    }
  }

  void _getQualityScoreResults(String email) async {
    try {
      final response = jsonDecode(await client.get(Uri.https('ipqualityscore.com', '/api/json/email/********************************/$email')).then((response) => response.body)) as Map<String, dynamic>;
      if (response['success']) {
        log('[getQualityScoreResults] Success!');
        qualityScoreResults.addAll([
          'valid: ${response['valid'] ?? ""}',
          'disposable: ${response['disposable'] ?? ""}',
          'honeypot: ${response['honeypot'] ?? ""}',
          'frequent_complainer: ${response['frequent_complainer'] ?? ""}',
          'spam_trap_score: ${response['spam_trap_score'] ?? ""}',
          'recent_abuse: ${response['recent_abuse'] ?? ""}',
          'fraud_score: ${response['fraud_score'] ?? ""}',
          'leaked: ${response['leaked'] ?? ""}'
        ]);
      } else {
        log('[getQualityScoreResults] Failed!');
        showSnackbar('QualityScore Failed!');
      }
    } catch (e) {
      log('[getQualityScoreResults] $e');
      showSnackbar('QualityScore Failed!');
    }
    counter.value += one;
  }

  void _getEmailRepResults(String email) async {
    try {
      final response = jsonDecode(await client.get(Uri.https('emailrep.io', '/$email')).then((response) => response.body)) as Map<String, dynamic>;
      if (response.isEmpty) {
        log('[getEmailRepResults] Empty Response!');
        showSnackbar('EmailRep Empty Response!');
        counter.value += one;
        return;
      } else if (response['status'] == 'fail') {
        log('[getEmailRepResults] Failed!');
        showSnackbar('EmailRep Failed!');
        counter.value += one;
        return;
      } else {
        log('[getEmailRepResults] Success!');
        log('[getEmailRepResults] ${response.toString()}');
        emailRepResults.addAll([
          'reputation: ${response['reputation'] ?? ""}',
          'suspicious: ${response['suspicious'] ?? ""}',
          'blacklisted: ${response['details']?['blacklisted'] ?? ""}',
          'malicious_activity: ${response['details']?['malicious_activity'] ?? ""}',
          'malicious_activity_recent: ${response['details']?['malicious_activity_recent'] ?? ""}',
          'credentials_leaked: ${response['details']?['credentials_leaked'] ?? ""}',
          'credentials_leaked_recent: ${response['details']?['credentials_leaked_recent'] ?? ""}',
          'data_breach: ${response['details']?['data_breach'] ?? ""}',
          'first_seen: ${response['details']?['first_seen'] ?? ""}',
          'last_seen: ${response['details']?['last_seen'] ?? ""}',
          'suspicious_tld: ${response['details']?['suspicious_tld'] ?? ""}',
          'spam: ${response['details']?['spam'] ?? ""}',
          'free_provider: ${response['details']?['free_provider'] ?? ""}',
          'disposable: ${response['details']?['disposable'] ?? ""}',
          'deliverable: ${response['details']?['deliverable'] ?? ""}',
          'accept_all: ${response['details']?['accept_all'] ?? ""}',
          'spoofable: ${response['details']?['spoofable'] ?? ""}',
          'profiles: ${response['details']?['profiles']?.join(', ') ?? ""}',
        ]);
      }
    } catch (e) {
      log('[getEmailRepResults] $e');
      showSnackbar('EmailRep Failed!');
    }
    counter.value += one;
  }

  void _getDBresults(String dbPath, String query) async {
    try {
      final db = await openDatabase(dbPath);
      final firstTable = (await db.query('sqlite_master', where: "type='table'")).first['name'] as String;
      final resultsEXACT = await db.query(firstTable, where: 'email = ?', whereArgs: [query]);

      if (resultsEXACT.isNotEmpty) {
        db.close();
        log('[getResults] EXACT Query "$query" gave ${resultsEXACT.length} results');
        dbResults.value = resultsEXACT;
      } else {
        final resultsLIKE = await db.query(firstTable, where: 'email LIKE ?', whereArgs: ['%$query%']);
        db.close();
        log('[getResults] LIKE Query "$query" gave ${resultsLIKE.length} results');
        dbResults.value = resultsLIKE;
      }
    } catch (e) {
      log('[getDBresults] $e');
      showSnackbar('DB Failed!');
      dbResults.value = [];
    }
    counter.value += one;
  }

  void _getProxyNovaResults(String filter, String query) async {
    try {
      final response = await client.get(Uri.https('api.proxynova.com', '/comb', {'query': query}));
      final body = jsonDecode(response.body);
      if (body['error'] != null) {
        log('[getProxyNovaResults] ${body['error']}, ${body['exception_message']}');
        showSnackbar('ProxyNova Failed!');
        return;
      }
      final lines = body['lines'];
      log('[getProxyNovaResults] query "$query" gave ${lines.length} results');
      if (filter.isNotEmpty && lines.isNotEmpty) {
        for (String line in lines) {
          if (line.toLowerCase().contains(filter.toLowerCase())) {
            proxyNovaResults.add(line);
          }
        }
        log('[getProxyNovaResults] filter "$filter" gave ${proxyNovaResults.length} results');
      } else {
        proxyNovaResults.value = lines;
      }
    } catch (e) {
      log('[getProxyNovaResults] $e');
      showSnackbar('ProxyNova Failed!');
    }
    counter.value += one;
  }

  void _getPsbDmpResults(String email) async {
    try {
      final response = await client.get(Uri.https('psbdmp.ws', '/api/v3/search/$email'));
      psbdmpResults.value = jsonDecode(response.body);
      log('[getPsbDmpResults] query "$email" gave ${psbdmpResults.length} results');
    } catch (e) {
      log('[getPsbDmpResults] $e');
      showSnackbar('PsbDmp Failed!');
    }
    counter.value += one;
  }
}

class EmailLookup extends StatelessWidget {
  final controller = Get.put(EmailLookupController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: trueValue,
      appBar: transparentAppBar(MODULES.emailLookup, windowControls()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: fifteen),
        child: ListView(
          children: [
            SizedBox(height: ten),
            SizedBox(
              height: SIZES.buttonHeight2,
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => controller._getQuery('Global Search', trueValue),
                icon: Icon(Icons.search),
                label: Text('Global Search', style: TextStyle(fontSize: SIZES.fontSizeL)),
              ),
            ),
            SizedBox(height: 7),
            Obx(() => ListView.builder(
                  shrinkWrap: trueValue,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: controller.dbPaths.length,
                  itemBuilder: (context, index) {
                    return Row(
                      children: [
                        Expanded(
                          child: SizedBox(
                            height: SIZES.buttonHeight2,
                            child: OutlinedButton(
                              onPressed: () => controller._getQuery(controller.dbPaths[index], falseValue),
                              style: OutlinedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(SIZES.borderRadius),
                              )),
                              child: Text(
                                getFileName(controller.dbPaths[index]).replaceFirst('.db', ''),
                                style: TextStyle(fontSize: SIZES.fontSizeL),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.all(SIZES.edgeInsets),
                          child: IconButton(
                              icon: Icon(Icons.delete_outline, size: SIZES.iconL),
                              onPressed: () {
                                controller._deleteFile(File(controller.dbPaths[index]));
                                controller.dbPaths.removeAt(index);
                                controller._saveDBs();
                              }),
                        ),
                      ],
                    );
                  },
                )),
          ],
        ),
      ),
      floatingActionButton: extendedTransparentFAB(controller._getDBs, 'Select Databases', 'Add Databases', Icons.folder),
    );
  }
}

class EmailLookupResults extends StatelessWidget {
  final controller = Get.find<EmailLookupController>();

  Card _resultCard(ColorSchemeNotifier colorScheme, String title, String subtitle, Function() onTap) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(ten),
        side: BorderSide(color: colorScheme.fgColor),
      ),
      child: ListTile(
        title: Text(title),
        subtitle: Text(subtitle, style: TextStyle(color: colorScheme.fgColor2)),
        onTap: onTap,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Get.find<ColorSchemeController>().colorScheme.value;
    final args = Get.arguments as Map<String, dynamic>;
    final query = args['query'].split('@')[0];
    if (args['dbPath'] == 'Global Search') {
      final domain = args['query'].split('@')[1];
      controller._getQualityScoreResults(args['query']);
      controller._getEmailRepResults(args['query']);
      controller.dbResults.value = [];
      controller.counter.value += one;
      controller._getProxyNovaResults(domain, query);
      controller._getPsbDmpResults(query);
    } else {
      String filter = '';
      final fileName = getFileName(args['dbPath']).toLowerCase();
      final domain = fileName.replaceFirst('.db', '');
      final email = args['query'] + '@' + domain;
      if (fileName.contains('.com')) {
        filter = domain;
      }
      controller._getQualityScoreResults(email);
      controller._getEmailRepResults(email);
      controller._getDBresults(args['dbPath'], query);
      controller._getProxyNovaResults(filter, query);
      controller._getPsbDmpResults(query);
    }

    return Obx(() => controller.counter.value != controller.maxCounter.value
        ? Center(child: SpinKitFadingFour(color: colorScheme.fgColor, size: hundred))
        : Scaffold(
            extendBodyBehindAppBar: trueValue,
            appBar: transparentAppBar('Results', windowControls()),
            body: Padding(
              padding: EdgeInsets.fromLTRB(fifteen, zero, fifteen, fifteen),
              child: ListView(children: [
                controller.qualityScoreResults.isNotEmpty ? _resultCard(colorScheme, 'Quality Score', controller.qualityScoreResults.join('\n'), () => copyToClipboard(controller.qualityScoreResults.join('\n'))) : Container(),
                controller.emailRepResults.isNotEmpty ? _resultCard(colorScheme, 'Email Rep', controller.emailRepResults.join('\n'), () => copyToClipboard(controller.emailRepResults.join('\n'))) : Container(),
                for (Map result in controller.dbResults) _resultCard(colorScheme, result['email'].toString(), result['password'].toString(), () => copyToClipboard(result['password'].toString())),
                for (String result in controller.proxyNovaResults)
                  Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(ten),
                      side: BorderSide(color: colorScheme.fgColor),
                    ),
                    child: ListTile(
                      title: Text(result, style: TextStyle(fontSize: 12)),
                      onTap: () => copyToClipboard(result),
                    ),
                  ),
                for (Map result in controller.psbdmpResults)
                  _resultCard(
                    colorScheme,
                    'pastebin.com/${result['id']}',
                    result['text'],
                    () => openUrl('https://pastebin.com/${result['id']}'),
                  ),
              ]),
            )));
  }
}
